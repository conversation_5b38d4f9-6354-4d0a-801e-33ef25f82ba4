-- Migration to add support for Receipts (Chi<PERSON>ț<PERSON>)

-- 1. Create the main table for receipts
CREATE TABLE IF NOT EXISTS receipts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    invoice_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL UNIQUE,
    issue_date DATE NOT NULL,
    amount DECIMAL(12, 4) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'RON',
    payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'card', 'bank_transfer', 'online')),
    client_name TEXT NOT NULL,
    client_details TEXT, -- CUI/CNP, address etc.
    notes TEXT,
    pdf_path TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (series_id) REFERENCES invoice_series(id)
);

-- 2. Update the 'invoices' table to better track payment status
-- We already added 'paid_amount', 'payment_method', 'paid_date' in a previous migration.
-- Let's add a more explicit 'payment_status' column.

ALTER TABLE invoices ADD COLUMN payment_status TEXT DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'partially_paid', 'paid'));

-- 3. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_receipts_company ON receipts(company_id);
CREATE INDEX IF NOT EXISTS idx_receipts_invoice ON receipts(invoice_id);

-- 4. Add a specific series type for receipts
INSERT OR IGNORE INTO invoice_series (company_id, name, series, prefix, year, current_number, type, active) VALUES
(1, 'Serie Chitanțe Implicită', 'CH', 'CH', strftime('%Y', 'now'), 0, 'receipt', TRUE); 