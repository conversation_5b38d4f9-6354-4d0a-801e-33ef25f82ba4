import { Hono } from 'hono';
import { z } from 'zod';
import type { Env } from '../index';
import { authMiddleware } from '../middleware/auth';
import type { D1PreparedStatement } from '@cloudflare/workers-types';
import { PDFService } from './invoices';

const creditNoteRoutes = new Hono<{ Bindings: Env, Variables: { user: any } }>();

// Apply auth middleware to all routes
creditNoteRoutes.use('*', authMiddleware);

// Validation schema for creating a credit note
const createCreditNoteSchema = z.object({
  original_invoice_id: z.number(),
  series_id: z.number(),
  issue_date: z.string(),
  reason: z.string().optional(),
  items: z.array(z.object({
    original_item_id: z.number().optional(),
    product_id: z.number().optional(),
    name: z.string(),
    quantity: z.number(),
    unit_price: z.number(),
    vat_rate: z.number()
  }))
});

// GET / - List all credit notes for the company
creditNoteRoutes.get('/', async (c) => {
  try {
    const user = c.get('user');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const offset = (page - 1) * limit;

    const creditNotesRes = await c.env.DB.prepare(`
      SELECT 
        cn.*,
        c.name as client_name,
        i.number as original_invoice_number
      FROM credit_notes cn
      JOIN clients c ON cn.client_id = c.id
      JOIN invoices i ON cn.original_invoice_id = i.id
      WHERE cn.company_id = ?
      ORDER BY cn.issue_date DESC
      LIMIT ? OFFSET ?
    `).bind(user.company_id, limit, offset).all();

    const totalResult: any = await c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM credit_notes WHERE company_id = ?
    `).bind(user.company_id).first();
    const totalCount = totalResult?.total || 0;

    return c.json({
      success: true,
      data: {
        credit_notes: creditNotesRes.results,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      }
    });

  } catch (error: any) {
    console.error('Error fetching credit notes:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch credit notes',
      error: error.message
    }, 500);
  }
});

// POST / - Create a new credit note
creditNoteRoutes.post('/', async (c) => {
  const user = c.get('user');
  const db = c.env.DB;

  try {
    const body = await c.req.json();
    const validation = createCreditNoteSchema.safeParse(body);

    if (!validation.success) {
      return c.json({ success: false, message: 'Invalid data', errors: validation.error.errors }, 400);
    }
    const data = validation.data;

    // Fetch the original invoice
    const originalInvoice: any = await db.prepare(
      `SELECT * FROM invoices WHERE id = ? AND company_id = ?`
    ).bind(data.original_invoice_id, user.company_id).first();

    if (!originalInvoice) {
      return c.json({ success: false, message: 'Original invoice not found' }, 404);
    }

    // Start a transaction
    const batch: D1PreparedStatement[] = [];

    // 1. Calculate totals for the credit note
    let subtotal = 0;
    let vatAmount = 0;
    for (const item of data.items) {
      const itemSubtotal = item.quantity * item.unit_price;
      const itemVat = itemSubtotal * (item.vat_rate / 100);
      subtotal += itemSubtotal;
      vatAmount += itemVat;
    }
    const total = subtotal + vatAmount;

    // Check if credit amount exceeds invoice balance
    const outstanding_balance = (originalInvoice.total || 0) - (originalInvoice.paid_amount || 0) - (originalInvoice.credited_amount || 0);
    if (total > outstanding_balance) {
      return c.json({ success: false, message: `Credit amount (${total}) cannot exceed the outstanding invoice balance (${outstanding_balance}).` }, 400);
    }
    
    // 2. Get next credit note number from the series
    const series: any = await db.prepare(
      `SELECT * FROM invoice_series WHERE id = ? AND company_id = ? AND type = 'credit_note'`
    ).bind(data.series_id, user.company_id).first();

    if (!series) {
      return c.json({ success: false, message: 'Credit note series not found or invalid' }, 400);
    }
    const nextNumber = (series.current_number || 0) + 1;
    const creditNoteNumber = `${series.prefix}${series.year}-${nextNumber.toString().padStart(3, '0')}`;
    
    // 3. Insert the credit note
    const creditNoteInsert = db.prepare(
      `INSERT INTO credit_notes (company_id, client_id, original_invoice_id, series_id, number, issue_date, reason, currency, subtotal, vat_amount, total)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
    ).bind(
      user.company_id,
      originalInvoice.client_id,
      data.original_invoice_id,
      data.series_id,
      creditNoteNumber,
      data.issue_date,
      data.reason || null,
      originalInvoice.currency,
      subtotal,
      vatAmount,
      total
    );
    // D1 doesn't directly return the last insert ID in batch, so we have to do this first.
    const creditNoteResult = await creditNoteInsert.run();
    const creditNoteId = creditNoteResult.meta.last_row_id;
    
    if (!creditNoteId) {
        throw new Error("Failed to create credit note, could not retrieve ID.");
    }

    // 4. Insert credit note items
    for (const item of data.items) {
      const itemSubtotal = item.quantity * item.unit_price;
      const itemVat = itemSubtotal * (item.vat_rate / 100);
      const itemTotal = itemSubtotal + itemVat;

      batch.push(
        db.prepare(
          `INSERT INTO credit_note_items (credit_note_id, original_item_id, product_id, name, quantity, unit_price, subtotal, vat_rate, vat_amount, total)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
        ).bind(
          creditNoteId,
          item.original_item_id || null,
          item.product_id || null,
          item.name,
          item.quantity,
          item.unit_price,
          itemSubtotal,
          item.vat_rate,
          itemVat,
          itemTotal
        )
      );
    }

    // 5. Update the invoice series number
    batch.push(
        db.prepare(`UPDATE invoice_series SET current_number = ? WHERE id = ?`)
          .bind(nextNumber, data.series_id)
    );

    // 6. Update the original invoice's credited amount and status
    const newCreditedAmount = (originalInvoice.credited_amount || 0) + total;
    const isFullyCredited = newCreditedAmount >= originalInvoice.total;
    
    batch.push(
        db.prepare(`UPDATE invoices SET credited_amount = ?, status_credited = ? WHERE id = ?`)
          .bind(newCreditedAmount, isFullyCredited ? 1 : 0, data.original_invoice_id)
    );

    await db.batch(batch);

    return c.json({
      success: true,
      message: 'Credit note created successfully',
      data: { credit_note_id: creditNoteId }
    }, 201);

  } catch (error: any) {
    console.error('Error creating credit note:', error);
    return c.json({ 
      success: false, 
      message: 'Failed to create credit note',
      error: error.message
    }, 500);
  }
});

// GET /:id - Get a single credit note by ID
creditNoteRoutes.get('/:id(\\d+)', async (c) => {
  try {
    const user = c.get('user');
    const id = parseInt(c.req.param('id')!);

    // Fetch credit note details
    const creditNote: any = await c.env.DB.prepare(`
      SELECT 
        cn.*,
        c.name as client_name,
        c.cui as client_cui,
        i.number as original_invoice_number
      FROM credit_notes cn
      JOIN clients c ON cn.client_id = c.id
      JOIN invoices i ON cn.original_invoice_id = i.id
      WHERE cn.id = ? AND cn.company_id = ?
    `).bind(id, user.company_id).first();

    if (!creditNote) {
      return c.json({ success: false, message: 'Credit note not found' }, 404);
    }

    // Fetch credit note items
    const itemsResult = await c.env.DB.prepare(
      `SELECT * FROM credit_note_items WHERE credit_note_id = ? ORDER BY sort_order`
    ).bind(id).all();

    return c.json({
      success: true,
      data: {
        credit_note: creditNote,
        items: itemsResult.results
      }
    });

  } catch (error: any) {
    console.error(`Error fetching credit note ${c.req.param('id')}:`, error);
    return c.json({ 
      success: false, 
      message: 'Failed to fetch credit note',
      error: error.message
    }, 500);
  }
});

// GET /:id/pdf - Generate a PDF for the credit note
creditNoteRoutes.get('/:id(\\d+)/pdf', async (c) => {
  try {
    const user = c.get('user');
    const id = parseInt(c.req.param('id')!);
    const db = c.env.DB;

    // Fetch credit note details
    const creditNote: any = await db.prepare(
      `SELECT cn.*, i.number as original_invoice_number FROM credit_notes cn 
       JOIN invoices i ON cn.original_invoice_id = i.id
       WHERE cn.id = ? AND cn.company_id = ?`
    ).bind(id, user.company_id).first();

    if (!creditNote) {
      return c.json({ success: false, message: 'Credit note not found' }, 404);
    }

    // Fetch company and client info
    const company: any = await db.prepare(`SELECT * FROM companies WHERE id = ?`).bind(user.company_id).first();
    const client: any = await db.prepare(`SELECT * FROM clients WHERE id = ?`).bind(creditNote.client_id).first();
    
    // Fetch items
    const itemsResult = await db.prepare(
      `SELECT * FROM credit_note_items WHERE credit_note_id = ? ORDER BY sort_order`
    ).bind(id).all();

    // Generate PDF
    const pdfBuffer = await PDFService.generateCreditNotePDF(
      creditNote,
      company,
      client,
      itemsResult.results as any[],
      creditNote.original_invoice_number,
      c.env
    );

    // Return PDF response
    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="Factura-Storno-${creditNote.number}.pdf"`
      }
    });

  } catch (error: any) {
    console.error(`Error generating PDF for credit note ${c.req.param('id')}:`, error);
    return c.json({ 
      success: false, 
      message: 'Failed to generate PDF',
      error: error.message
    }, 500);
  }
});

export { creditNoteRoutes }; 