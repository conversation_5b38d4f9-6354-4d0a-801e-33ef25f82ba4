{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "preserveValueImports": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true, "declaration": false, "declarationMap": false, "sourceMap": false, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["@cloudflare/workers-types", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}