import { Hono } from 'hono';
import { z } from 'zod';
import type { Env } from '../index';
import { authMiddleware } from '../middleware/auth';
import type { D1PreparedStatement } from '@cloudflare/workers-types';
import { PDFService } from './invoices';

const receiptRoutes = new Hono<{ Bindings: Env, Variables: { user: any } }>();

receiptRoutes.use('*', authMiddleware);

const createReceiptSchema = z.object({
  invoice_id: z.number(),
  series_id: z.number(),
  issue_date: z.string(),
  amount: z.number().positive(),
  payment_method: z.enum(['cash', 'card', 'bank_transfer', 'online']),
  notes: z.string().optional()
});

// GET / - List all receipts
receiptRoutes.get('/', async (c) => {
  const user = c.get('user');
  const db = c.env.DB;
  
  try {
    const receipts = await db.prepare(
      `SELECT r.*, i.number as invoice_number FROM receipts r
       JOIN invoices i ON r.invoice_id = i.id
       WHERE r.company_id = ? ORDER BY r.issue_date DESC`
    ).bind(user.company_id).all();

    return c.json({ success: true, data: receipts.results });
  } catch (error: any) {
    return c.json({ success: false, message: 'Failed to fetch receipts', error: error.message }, 500);
  }
});

// POST / - Create a new receipt
receiptRoutes.post('/', async (c) => {
  const user = c.get('user');
  const db = c.env.DB;

  try {
    const body = await c.req.json();
    const validation = createReceiptSchema.safeParse(body);

    if (!validation.success) {
      return c.json({ success: false, message: 'Invalid data', errors: validation.error.errors }, 400);
    }
    const data = validation.data;

    const invoice: any = await db.prepare(
      `SELECT * FROM invoices WHERE id = ? AND company_id = ?`
    ).bind(data.invoice_id, user.company_id).first();

    if (!invoice) {
      return c.json({ success: false, message: 'Invoice not found' }, 404);
    }

    const client: any = await db.prepare(`SELECT * FROM clients WHERE id = ?`).bind(invoice.client_id).first();

    // Check if payment exceeds outstanding balance
    const outstanding_balance = (invoice.total || 0) - (invoice.paid_amount || 0) - (invoice.credited_amount || 0);
    if (data.amount > outstanding_balance) {
      return c.json({ success: false, message: `Receipt amount (${data.amount}) cannot exceed the outstanding invoice balance (${outstanding_balance}).` }, 400);
    }

    // Get series and number
    const series: any = await db.prepare(`SELECT * FROM invoice_series WHERE id = ? AND type = 'receipt'`).bind(data.series_id).first();
    if (!series) {
        return c.json({ success: false, message: 'Receipt series not found' }, 400);
    }
    const nextNumber = (series.current_number || 0) + 1;
    const receiptNumber = `${series.prefix}${series.year}-${nextNumber.toString().padStart(3, '0')}`;

    // Use a transaction
    const batch: D1PreparedStatement[] = [];

    // 1. Insert receipt
    const receiptInsertResult = await db.prepare(
      `INSERT INTO receipts (company_id, invoice_id, series_id, number, issue_date, amount, payment_method, client_name, client_details, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
    ).bind(
        user.company_id, data.invoice_id, data.series_id, receiptNumber, data.issue_date, data.amount, data.payment_method,
        client.name, `CUI: ${client.cui}, Adresa: ${client.address}`, data.notes || null
    ).run();

    const receiptId = receiptInsertResult.meta.last_row_id;
    if (!receiptId) throw new Error("Failed to create receipt.");

    // 2. Update invoice paid_amount and payment_status
    const newPaidAmount = (invoice.paid_amount || 0) + data.amount;
    const newOutstanding = invoice.total - newPaidAmount - (invoice.credited_amount || 0);
    const paymentStatus = newOutstanding <= 0 ? 'paid' : 'partially_paid';

    batch.push(
      db.prepare(`UPDATE invoices SET paid_amount = ?, payment_status = ?, paid_date = ? WHERE id = ?`)
      .bind(newPaidAmount, paymentStatus, data.issue_date, data.invoice_id)
    );

    // 3. Update series number
    batch.push(
      db.prepare(`UPDATE invoice_series SET current_number = ? WHERE id = ?`).bind(nextNumber, data.series_id)
    );

    await db.batch(batch);

    return c.json({ success: true, message: 'Receipt created successfully', data: { receipt_id: receiptId }}, 201);

  } catch (error: any) {
    return c.json({ success: false, message: 'Failed to create receipt', error: error.message }, 500);
  }
});

receiptRoutes.get('/:id(\\d+)/pdf', async (c) => {
  const user = c.get('user');
  const db = c.env.DB;
  const receiptId = parseInt(c.req.param('id')!);

  try {
    const receipt: any = await db.prepare(
      `SELECT r.*, i.number as invoice_number FROM receipts r
       JOIN invoices i ON r.invoice_id = i.id
       WHERE r.id = ? AND r.company_id = ?`
    ).bind(receiptId, user.company_id).first();

    if (!receipt) {
      return c.json({ success: false, message: 'Receipt not found' }, 404);
    }
    
    const company: any = await db.prepare(`SELECT * FROM companies WHERE id = ?`).bind(user.company_id).first();
    const invoice: any = await db.prepare(`SELECT * FROM invoices WHERE id = ?`).bind(receipt.invoice_id).first();
    const client: any = await db.prepare(`SELECT * FROM clients WHERE id = ?`).bind(invoice.client_id).first();
    
    const pdfBuffer = await PDFService.generateReceiptPDF(receipt, company, client, receipt.invoice_number, c.env);

    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="Chitanta-${receipt.number}.pdf"`
      }
    });

  } catch (error: any) {
    return c.json({ success: false, message: 'Failed to generate receipt PDF', error: error.message }, 500);
  }
});

export { receiptRoutes }; 