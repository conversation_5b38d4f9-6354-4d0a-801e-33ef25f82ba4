-- Migration to add support for Credit Notes (Facturi Storno)

-- 1. Create the main table for credit notes
CREATE TABLE IF NOT EXISTS credit_notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    original_invoice_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    number TEXT NOT NULL UNIQUE,
    issue_date DATE NOT NULL,
    reason TEXT,
    currency TEXT NOT NULL DEFAULT 'RON',
    subtotal DECIMAL(12, 4) NOT NULL,
    vat_amount DECIMAL(12, 4) NOT NULL,
    total DECIMAL(12, 4) NOT NULL,
    status TEXT NOT NULL DEFAULT 'issued' CHECK (status IN ('draft', 'issued', 'cancelled')),
    pdf_path TEXT,
    xml_path TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    <PERSON><PERSON><PERSON>G<PERSON> KEY (company_id) REFERENCES companies(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (original_invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (series_id) REFERENCES invoice_series(id)
);

-- 2. Create a table for credit note line items
CREATE TABLE IF NOT EXISTS credit_note_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    credit_note_id INTEGER NOT NULL,
    original_item_id INTEGER, -- Optional link to the original invoice_items.id
    product_id INTEGER,
    name TEXT NOT NULL,
    quantity DECIMAL(10, 4) NOT NULL,
    unit_price DECIMAL(10, 4) NOT NULL,
    subtotal DECIMAL(12, 4) NOT NULL,
    vat_rate DECIMAL(5, 2) NOT NULL,
    vat_amount DECIMAL(12, 4) NOT NULL,
    total DECIMAL(12, 4) NOT NULL,
    sort_order INTEGER DEFAULT 0,

    FOREIGN KEY (credit_note_id) REFERENCES credit_notes(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (original_item_id) REFERENCES invoice_items(id)
);

-- 3. Update the existing 'invoices' table
-- Add a new status 'credited' to the invoices table status checks if it's not already there.
-- SQLite doesn't have a simple way to add to a CHECK constraint, so this often requires a table rebuild in production.
-- For development, we assume we can manage this, but in a real scenario, a more complex migration is needed.
-- We will add a 'credited_amount' and 'is_credited' flag for easier querying.

-- First, let's add the columns, ignoring errors if they already exist.
ALTER TABLE invoices ADD COLUMN credited_amount DECIMAL(12, 4) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN status_credited BOOLEAN DEFAULT FALSE; -- Simple flag

-- We can't easily modify the CHECK constraint, but we can update the application logic
-- to handle a new 'credited' status string alongside 'draft', 'sent', 'paid', 'overdue', 'cancelled'.
-- For now, we will rely on the 'status_credited' flag and 'credited_amount'.


-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_credit_notes_company ON credit_notes(company_id);
CREATE INDEX IF NOT EXISTS idx_credit_notes_original_invoice ON credit_notes(original_invoice_id);
CREATE INDEX IF NOT EXISTS idx_credit_note_items_credit_note ON credit_note_items(credit_note_id);

-- Optional: Add a specific series type for credit notes if needed
-- This allows for separate numbering schemes, e.g., 'S' for storno.
INSERT OR IGNORE INTO invoice_series (company_id, name, series, prefix, year, current_number, type, active) VALUES
(1, 'Serie Storno Implicită', 'S', 'S', strftime('%Y', 'now'), 0, 'credit_note', TRUE); 