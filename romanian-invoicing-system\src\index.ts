import { Hono } from 'hono';
import type { D1Database, KVNamespace, R2Bucket, Fetcher, SendEmail } from '@cloudflare/workers-types';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { serveStatic } from 'hono/cloudflare-workers';
import { authRoutes } from './routes/auth';
import { invoices } from './routes/invoices';
import { clientRoutes } from './routes/clients';
import { anaf } from './routes/anaf';
import { productRoutes } from './routes/products';
import { companyRoutes } from './routes/companies';
import { dashboardRoutes } from './routes/dashboard';
import { reportRoutes } from './routes/reports';
import { settingsRoutes } from './routes/settings';
import { paymentRoutes } from './routes/payments';
import { creditNoteRoutes } from './routes/credit_notes';
import { receiptRoutes } from './routes/receipts';
import { authMiddleware } from './middleware/auth';
import { errorHandler } from './middleware/error-handler';

export interface Env {
  DB: D1Database;
  KV: KVNamespace;
  BUCKET: R2Bucket;
  EMAIL: SendEmail;
  JWT_SECRET: string;
  ENVIRONMENT: string;
  COMPANY_NAME: string;
  DEFAULT_TIMEZONE: string;
  ENCRYPTION_KEY: string;
  BROWSER: Fetcher;
  INVOICING_KV: KVNamespace;
  INVOICING_FILES: R2Bucket;
}

const app = new Hono<{ Bindings: Env }>();

// Global middleware
app.use('*', logger());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// Serve static assets
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
app.use('/assets/*', serveStatic({ root: './' }));

// Serve the main application HTML
app.get('/', (c) => {
  // @ts-ignore - Ignore TS errors for embedded JavaScript in HTML template
  return c.html(`
    <!DOCTYPE html>
    <html lang="ro">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sistem Facturare Românesc - Soluție completă de facturare</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .gradient-bg {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
            .card-shadow {
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            }
        </style>
    </head>
    <body class="bg-gray-50 font-sans">
        <div id="app">
            <!-- Loading Screen -->
            <div id="loading" class="fixed inset-0 gradient-bg flex items-center justify-center z-50">
                <div class="text-center text-white">
                    <div class="mb-4">
                        <i class="fas fa-file-invoice text-6xl mb-4"></i>
                    </div>
                    <h1 class="text-3xl font-bold mb-2">Sistem Facturare Românesc</h1>
                    <p class="text-lg opacity-90 mb-6">Soluție completă de facturare conformă cu legislația română</p>
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                </div>
            </div>

            <!-- Login and Register Forms -->
            <div id="loginForm" class="hidden min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8">
                    <div class="text-center">
                        <i class="fas fa-file-invoice text-6xl text-indigo-600 mb-4"></i>
                        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                            Conectează-te la cont
                        </h2>
                        <p class="mt-2 text-sm text-gray-600">
                            Sistem complet de facturare pentru România
                        </p>
                    </div>
                    <form class="mt-8 space-y-6" onsubmit="handleLogin(event)">
                        <div class="rounded-md shadow-sm -space-y-px">
                            <div>
                                <label for="email" class="sr-only">Adresa de email</label>
                                <input id="email" name="email" type="email" required 
                                       class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm" 
                                       placeholder="Adresa de email">
                            </div>
                            <div>
                                <label for="password" class="sr-only">Parola</label>
                                <input id="password" name="password" type="password" required 
                                       class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm" 
                                       placeholder="Parola">
                            </div>
                        </div>

                        <div>
                            <button type="submit" 
                                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                    <i class="fas fa-lock text-indigo-500 group-hover:text-indigo-400"></i>
                                </span>
                                Conectează-te
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="text-sm text-gray-600">
                                Nu ai cont? 
                                <a href="#" onclick="showRegisterForm()" class="font-medium text-indigo-600 hover:text-indigo-500">
                                    Înregistrează-te aici
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <div id="registerForm" class="hidden min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8">
                    <div class="text-center">
                        <i class="fas fa-building text-6xl text-indigo-600 mb-4"></i>
                        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                            Înregistrează compania
                        </h2>
                        <p class="mt-2 text-sm text-gray-600">
                            Creează un cont nou pentru compania ta
                        </p>
                    </div>
                    <form class="mt-8 space-y-6" onsubmit="handleRegister(event)">
                        <div class="space-y-4">
                            <div>
                                <label for="company_name" class="block text-sm font-medium text-gray-700">Numele companiei</label>
                                <input id="company_name" name="company_name" type="text" required 
                                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                       placeholder="ex. Exemplu S.R.L.">
                            </div>
                            <div>
                                <label for="cui" class="block text-sm font-medium text-gray-700">CUI/CIF</label>
                                <input id="cui" name="cui" type="text" required 
                                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                       placeholder="ex. RO12345678">
                            </div>
                            <div>
                                <label for="admin_name" class="block text-sm font-medium text-gray-700">Numele administratorului</label>
                                <input id="admin_name" name="admin_name" type="text" required 
                                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                       placeholder="Numele și prenumele">
                            </div>
                            <div>
                                <label for="admin_email" class="block text-sm font-medium text-gray-700">Email administrator</label>
                                <input id="admin_email" name="admin_email" type="email" required 
                                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                       placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label for="admin_password" class="block text-sm font-medium text-gray-700">Parola</label>
                                <input id="admin_password" name="admin_password" type="password" required 
                                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                       placeholder="Minimum 8 caractere">
                            </div>
                        </div>

                        <div>
                            <button type="submit" 
                                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                    <i class="fas fa-plus text-indigo-500 group-hover:text-indigo-400"></i>
                                </span>
                                Înregistrează compania
                            </button>
                        </div>

                        <div class="text-center">
                            <p class="text-sm text-gray-600">
                                Ai deja cont? 
                                <a href="#" onclick="showLoginForm()" class="font-medium text-indigo-600 hover:text-indigo-500">
                                    Conectează-te aici
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Main Application Dashboard -->
            <div id="dashboard-main" class="hidden h-screen flex">
                <!-- Sidebar -->
                <aside class="w-64 bg-white shadow-md">
                    <div class="p-6">
                        <h2 class="text-xl font-bold text-gray-800">Meniu</h2>
                    </div>
                    <nav class="mt-2">
                        <a href="#" onclick="showSection('dashboard')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-tachometer-alt mr-3"></i> Tablou de Bord
                        </a>
                        <a href="#" onclick="showSection('invoices')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-file-invoice mr-3"></i> Facturi
                        </a>
                        <a href="#" onclick="showSection('credit-notes')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-file-contract mr-3"></i> Note de Credit
                        </a>
                        <a href="#" onclick="showSection('recurring')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-redo-alt mr-3"></i> Facturi Recurente
                        </a>
                        <a href="#" onclick="showSection('proformas')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-file-alt mr-3"></i> Proforme
                        </a>
                        <a href="#" onclick="showSection('receipts')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-receipt mr-3"></i> Chitanțe
                        </a>
                        <a href="#" onclick="showSection('clients')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-users mr-3"></i> Clienți
                        </a>
                        <a href="#" onclick="showSection('products')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-box-open mr-3"></i> Produse/Servicii
                        </a>
                        <a href="#" onclick="showSection('reports')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-chart-pie mr-3"></i> Rapoarte
                        </a>
                        <a href="#" onclick="showSection('settings')" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-200">
                            <i class="fas fa-cog mr-3"></i> Setări
                        </a>
                    </nav>
                </aside>

                <!-- Main Content -->
                <main class="flex-1 p-6 bg-gray-100 overflow-y-auto">
                    <div id="dashboard" class="page-section"></div>
                    <div id="invoices" class="page-section hidden"></div>
                    <div id="credit-notes" class="page-section hidden"></div>
                    <div id="recurring" class="page-section hidden"></div>
                    <div id="proformas" class="page-section hidden"></div>
                    <div id="receipts" class="page-section hidden"></div>
                    <div id="clients" class="page-section hidden"></div>
                    <div id="products" class="page-section hidden"></div>
                    <div id="reports" class="page-section hidden"></div>
                    <div id="settings" class="page-section hidden"></div>
                </main>
            </div>
        </div>

        <div id="modal-container"></div>
        
        <script>
            let authToken = localStorage.getItem('authToken');

            // Initialize app
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(() => {
                    document.getElementById('loading').classList.add('hidden');
                    if (authToken) {
                        loadDashboard();
                    } else {
                        showLoginForm();
                    }
                }, 1500);
            });

            function showLoginForm() {
                document.getElementById('registerForm').classList.add('hidden');
                document.getElementById('dashboard-main').classList.add('hidden');
                document.getElementById('loginForm').classList.remove('hidden');
            }

            function showRegisterForm() {
                document.getElementById('loginForm').classList.add('hidden');
                document.getElementById('dashboard').classList.add('hidden');
                document.getElementById('registerForm').classList.remove('hidden');
            }

            function loadDashboard() {
                document.getElementById('loginForm').classList.add('hidden');
                document.getElementById('registerForm').classList.add('hidden');
                document.getElementById('dashboard').classList.remove('hidden');
                showSection('overview');
            }

            async function handleLogin(event) {
                event.preventDefault();
                const formData = new FormData(event.target);
                
                try {
                    const response = await fetch('/api/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            email: formData.get('email'),
                            password: formData.get('password')
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok) {
                        authToken = data.token;
                        localStorage.setItem('authToken', authToken);
                        loadDashboard();
                    } else {
                        alert('Eroare la conectare: ' + data.message);
                    }
                } catch (error) {
                    alert('Eroare la conectare: ' + error.message);
                }
            }

            async function handleRegister(event) {
                event.preventDefault();
                const formData = new FormData(event.target);
                
                try {
                    const response = await fetch('/api/auth/register', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            company_name: formData.get('company_name'),
                            cui: formData.get('cui'),
                            admin_name: formData.get('admin_name'),
                            admin_email: formData.get('admin_email'),
                            admin_password: formData.get('admin_password')
                        })
                    });

                    const data = await response.json();
                    
                    if (response.ok) {
                        alert('Compania a fost înregistrată cu succes! Te poți conecta acum.');
                        showLoginForm();
                    } else {
                        alert('Eroare la înregistrare: ' + data.message);
                    }
                } catch (error) {
                    alert('Eroare la înregistrare: ' + error.message);
                }
            }

            function logout() {
                localStorage.removeItem('authToken');
                authToken = null;
                showLoginForm();
            }

            function showSection(sectionId) {
                document.querySelectorAll('.page-section').forEach(section => {
                    section.classList.add('hidden');
                });
                document.getElementById(sectionId).classList.remove('hidden');

                if (sectionId === 'dashboard') loadDashboardData();
                if (sectionId === 'invoices') fetchInvoicesForTable();
                if (sectionId === 'credit-notes') fetchCreditNotes();
                if (sectionId === 'recurring') fetchRecurringInvoices();
                if (sectionId === 'clients') fetchClientsForTable();
                if (sectionId === 'products') fetchProductsForTable();
                if (sectionId === 'receipts') fetchReceipts();
                if (sectionId === 'settings') loadSettings();
            }

            // Define fetchInvoices function without template literals
            function fetchInvoices() {
                // Use a closure to avoid TypeScript checking
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/invoices', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea facturilor');

                        const tbody = document.getElementById('invoiceTableBody');
                        if (!tbody) return;
                        tbody.innerHTML = '';

                        // Update here to handle the correct response structure
                        const invoices = data.data?.invoices || [];
                        
                        if (invoices.length === 0) {
                            const row = document.createElement('tr');
                            row.innerHTML = '<td colspan="6" class="px-6 py-4 text-center text-gray-500">Nu există facturi.</td>';
                            tbody.appendChild(row);
                            return;
                        }

                        for (const invoice of invoices) {
                            const tr = document.createElement('tr');
                            const statusBadge = invoice.paid ? 
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Plătită</span>' : 
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Neplătită</span>';
                            
                            // Build row content without template literals
                            let rowContent = '';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + invoice.series + invoice.number + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + new Date(invoice.issue_date).toLocaleDateString('ro-RO') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + (invoice.client_name || invoice.client_id) + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right">' + invoice.total.toFixed(2) + ' ' + invoice.currency + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-center">' + statusBadge + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">';
                            rowContent += '<a href="/api/invoices/' + invoice.id + '/pdf" target="_blank" class="text-indigo-600 hover:text-indigo-900 mr-3" title="PDF"><i class="fas fa-file-pdf"></i></a>';
                            rowContent += '<a href="/api/invoices/' + invoice.id + '/xml" target="_blank" class="text-green-600 hover:text-green-900 mr-3" title="XML"><i class="fas fa-file-code"></i></a>';
                            rowContent += '</td>';
                            
                            tr.innerHTML = rowContent;
                            tbody.appendChild(tr);
                        }
                    } catch (err) {
                        console.error(err);
                        alert(err.message || 'An error occurred');
                    }
                })();
            }

            // Load enhanced dashboard data
            async function loadDashboardData() {
                const content = document.getElementById('content');
                content.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-blue-500"></i><p class="mt-2">Se încarcă...</p></div>';

                try {
                    const token = localStorage.getItem('authToken');
                    const response = await fetch('/api/dashboard/stats', {
                        headers: { 'Authorization': 'Bearer ' + token }
                    });

                    if (!response.ok) throw new Error('Failed to load dashboard data');

                    const data = await response.json();
                    const stats = data.data;

                    content.innerHTML = generateDashboardHTML(stats);

                    // Load charts if data is available
                    if (stats.monthly_trend && stats.monthly_trend.length > 0) {
                        loadRevenueChart(stats.monthly_trend);
                    }

                } catch (error) {
                    console.error('Dashboard loading error:', error);
                    content.innerHTML = '<div class="text-center py-8 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Eroare la încărcarea datelor</p></div>';
                }
            }

            function generateDashboardHTML(stats) {
                const overview = stats.overview || {};
                const overdue = stats.overdue || {};
                const counts = stats.counts || {};
                const recentInvoices = stats.recent_invoices || [];
                const topClients = stats.top_clients || [];

                return '<h1 class="text-3xl font-bold text-gray-900 mb-6">Tablou de bord</h1>' +

                    '<!-- KPI Cards -->' +
                    '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">' +
                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<div class="flex items-center">' +
                                '<div class="p-3 rounded-full bg-blue-500 bg-opacity-10">' +
                                    '<i class="fas fa-file-invoice text-2xl text-blue-500"></i>' +
                                '</div>' +
                                '<div class="ml-4">' +
                                    '<p class="text-sm text-gray-600">Facturi luna aceasta</p>' +
                                    '<p class="text-2xl font-semibold text-gray-900">' + (overview.invoices_this_month || 0) + '</p>' +
                                    (overview.invoice_growth ? '<p class="text-xs ' + (overview.invoice_growth >= 0 ? 'text-green-600' : 'text-red-600') + '">' + (overview.invoice_growth >= 0 ? '+' : '') + overview.invoice_growth + '%</p>' : '') +
                                '</div>' +
                            '</div>' +
                        '</div>' +

                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<div class="flex items-center">' +
                                '<div class="p-3 rounded-full bg-green-500 bg-opacity-10">' +
                                    '<i class="fas fa-money-bill-wave text-2xl text-green-500"></i>' +
                                '</div>' +
                                '<div class="ml-4">' +
                                    '<p class="text-sm text-gray-600">Venituri luna aceasta</p>' +
                                    '<p class="text-2xl font-semibold text-gray-900">' + (overview.revenue_this_month || 0).toFixed(2) + ' RON</p>' +
                                    (overview.revenue_growth ? '<p class="text-xs ' + (overview.revenue_growth >= 0 ? 'text-green-600' : 'text-red-600') + '">' + (overview.revenue_growth >= 0 ? '+' : '') + overview.revenue_growth + '%</p>' : '') +
                                '</div>' +
                            '</div>' +
                        '</div>' +

                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<div class="flex items-center">' +
                                '<div class="p-3 rounded-full bg-yellow-500 bg-opacity-10">' +
                                    '<i class="fas fa-clock text-2xl text-yellow-500"></i>' +
                                '</div>' +
                                '<div class="ml-4">' +
                                    '<p class="text-sm text-gray-600">Facturi restante</p>' +
                                    '<p class="text-2xl font-semibold text-gray-900">' + (overdue.count || 0) + '</p>' +
                                    '<p class="text-xs text-red-600">' + (overdue.amount || 0).toFixed(2) + ' RON</p>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +

                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<div class="flex items-center">' +
                                '<div class="p-3 rounded-full bg-purple-500 bg-opacity-10">' +
                                    '<i class="fas fa-users text-2xl text-purple-500"></i>' +
                                '</div>' +
                                '<div class="ml-4">' +
                                    '<p class="text-sm text-gray-600">Clienți activi</p>' +
                                    '<p class="text-2xl font-semibold text-gray-900">' + (counts.active_clients || 0) + '</p>' +
                                    '<p class="text-xs text-gray-500">din ' + (counts.total_clients || 0) + ' total</p>' +
                                '</div>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +

                    '<!-- Charts and Recent Activity -->' +
                    '<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">' +
                        '<div class="lg:col-span-2 bg-white p-6 rounded-lg card-shadow">' +
                            '<h3 class="text-lg font-semibold text-gray-900 mb-4">Evoluția veniturilor (ultimele 6 luni)</h3>' +
                            '<canvas id="revenueChart" width="400" height="200"></canvas>' +
                        '</div>' +

                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<h3 class="text-lg font-semibold text-gray-900 mb-4">Top clienți</h3>' +
                            '<div class="space-y-3">' +
                                topClients.slice(0, 5).map(function(client) {
                                    return '<div class="flex justify-between items-center p-2 bg-gray-50 rounded">' +
                                        '<div>' +
                                            '<p class="font-medium text-sm">' + client.name + '</p>' +
                                            '<p class="text-xs text-gray-600">' + client.invoice_count + ' facturi</p>' +
                                        '</div>' +
                                        '<div class="text-right">' +
                                            '<p class="font-medium text-sm">' + (client.total_revenue || 0).toFixed(2) + ' RON</p>' +
                                        '</div>' +
                                    '</div>';
                                }).join('') +
                            '</div>' +
                        '</div>' +
                    '</div>' +

                    '<!-- Recent Invoices and Quick Actions -->' +
                    '<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">' +
                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<h3 class="text-lg font-semibold text-gray-900 mb-4">Facturi recente</h3>' +
                            '<div class="space-y-3">' +
                                recentInvoices.slice(0, 5).map(function(invoice) {
                                    return '<div class="flex justify-between items-center p-3 bg-gray-50 rounded">' +
                                        '<div>' +
                                            '<p class="font-medium">' + invoice.series + invoice.number + '</p>' +
                                            '<p class="text-sm text-gray-600">' + invoice.client_name + '</p>' +
                                        '</div>' +
                                        '<div class="text-right">' +
                                            '<p class="font-medium">' + (invoice.total || 0).toFixed(2) + ' ' + invoice.currency + '</p>' +
                                            '<span class="text-xs px-2 py-1 rounded-full ' + (invoice.paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800') + '">' +
                                                (invoice.paid ? 'Plătită' : 'Neplătită') +
                                            '</span>' +
                                        '</div>' +
                                    '</div>';
                                }).join('') +
                            '</div>' +
                        '</div>' +

                        '<div class="bg-white p-6 rounded-lg card-shadow">' +
                            '<h3 class="text-lg font-semibold text-gray-900 mb-4">Acțiuni rapide</h3>' +
                            '<div class="space-y-3">' +
                                '<button onclick="showNewInvoiceModal()" class="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition">' +
                                    '<i class="fas fa-plus-circle text-blue-500 mr-2"></i>' +
                                    'Factură nouă' +
                                '</button>' +
                                '<button onclick="showSection(&#39;recurring&#39;)" class="w-full text-left p-3 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition">' +
                                    '<i class="fas fa-redo text-indigo-500 mr-2"></i>' +
                                    'Factură recurentă' +
                                '</button>' +
                                '<button onclick="showSection(&#39;clients&#39;)" class="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg transition">' +
                                    '<i class="fas fa-user-plus text-green-500 mr-2"></i>' +
                                    'Client nou' +
                                '</button>' +
                                '<button onclick="showSection(&#39;products&#39;)" class="w-full text-left p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition">' +
                                    '<i class="fas fa-box text-purple-500 mr-2"></i>' +
                                    'Produs/Serviciu nou' +
                                '</button>' +
                                '<button onclick="showSection(&#39;reports&#39;)" class="w-full text-left p-3 bg-orange-50 hover:bg-orange-100 rounded-lg transition">' +
                                    '<i class="fas fa-chart-bar text-orange-500 mr-2"></i>' +
                                    'Vezi rapoarte' +
                                '</button>' +
                            '</div>' +
                        '</div>' +
                    '</div>';
            }

            function loadRevenueChart(monthlyData) {
                // Simple chart implementation using Chart.js (would need to include the library)
                // For now, just a placeholder
                const canvas = document.getElementById('revenueChart');
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    ctx.fillStyle = '#e5e7eb';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    ctx.fillStyle = '#374151';
                    ctx.font = '16px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText('Grafic venituri (necesită Chart.js)', canvas.width/2, canvas.height/2);
                }
            }

            // Fetch recurring invoices function
            function fetchRecurringInvoices() {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/invoices/recurring', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea facturilor recurente');

                        const tbody = document.getElementById('recurringTableBody');
                        if (!tbody) return;
                        tbody.innerHTML = '';

                        const recurringInvoices = data.data || [];

                        if (recurringInvoices.length === 0) {
                            const row = document.createElement('tr');
                            row.innerHTML = '<td colspan="6" class="px-6 py-4 text-center text-gray-500">Nu există facturi recurente.</td>';
                            tbody.appendChild(row);
                            return;
                        }

                        for (const recurring of recurringInvoices) {
                            const tr = document.createElement('tr');
                            const statusBadge = recurring.is_active ?
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Activă</span>' :
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactivă</span>';

                            let rowContent = '';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + recurring.template_name + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + recurring.client_name + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + recurring.frequency + ' (la ' + recurring.interval_count + ')</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + new Date(recurring.next_generation_date).toLocaleDateString('ro-RO') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-center">' + statusBadge + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">';
                            rowContent += '<button onclick="editRecurring(' + recurring.id + ')" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Editează"><i class="fas fa-edit"></i></button>';
                            rowContent += '<button onclick="deleteRecurring(' + recurring.id + ')" class="text-red-600 hover:text-red-900" title="Șterge"><i class="fas fa-trash"></i></button>';
                            rowContent += '</td>';

                            tr.innerHTML = rowContent;
                            tbody.appendChild(tr);
                        }
                    } catch (err) {
                        console.error(err);
                        alert(err.message || 'An error occurred');
                    }
                })();
            }

            // Show new recurring invoice modal
            function showNewRecurringModal() {
                // Implementation for recurring invoice modal
                alert('Funcționalitatea pentru facturi recurente va fi implementată în curând!');
            }

            // Edit recurring invoice
            function editRecurring(id) {
                alert('Editarea facturilor recurente va fi implementată în curând!');
            }

            // Delete recurring invoice
            function deleteRecurring(id) {
                if (confirm('Sigur doriți să ștergeți această factură recurentă?')) {
                    (async function() {
                        try {
                            const token = localStorage.getItem('authToken');
                            const res = await fetch('/api/invoices/recurring/' + id, {
                                method: 'DELETE',
                                headers: {
                                    'Authorization': 'Bearer ' + token
                                }
                            });
                            const data = await res.json();
                            if (!res.ok) throw new Error(data.message || 'Eroare la ștergerea facturii recurente');

                            alert('Factura recurentă a fost ștearsă cu succes!');
                            fetchRecurringInvoices(); // Refresh the list
                        } catch (err) {
                            console.error(err);
                            alert(err.message || 'An error occurred');
                        }
                    })();
                }
            }

            // Admin functions for testing and migration
            function runMigration() {
                if (confirm('Sigur doriți să rulați migrarea bazei de date? Aceasta va crea tabelele necesare pentru noile funcționalități.')) {
                    (async function() {
                        try {
                            const token = localStorage.getItem('authToken');
                            const res = await fetch('/api/migrate', {
                                method: 'POST',
                                headers: {
                                    'Authorization': 'Bearer ' + token
                                }
                            });
                            const data = await res.json();
                            if (!res.ok) throw new Error(data.message || 'Eroare la migrare');

                            alert('Migrarea a fost completată cu succes!');
                        } catch (err) {
                            console.error(err);
                            alert('Eroare la migrare: ' + err.message);
                        }
                    })();
                }
            }

            function testDatabase() {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/invoices/test-db', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la testarea bazei de date');

                        alert('Test baza de date:\\n' +
                              'Tabel recurring_invoices există: ' + (data.data.recurring_table_exists ? 'DA' : 'NU') + '\\n' +
                              'Tabele disponibile: ' + data.data.all_tables.join(', '));
                    } catch (err) {
                        console.error(err);
                        alert('Eroare la testarea bazei de date: ' + err.message);
                    }
                })();
            }

            function testRecurringAPI() {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/invoices/recurring', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la testarea API-ului');

                        alert('Test API facturi recurente:\\n' +
                              'Status: ' + (data.success ? 'SUCCES' : 'EROARE') + '\\n' +
                              'Numărul de facturi recurente: ' + (data.data ? data.data.length : 0));
                    } catch (err) {
                        console.error(err);
                        alert('Eroare la testarea API-ului: ' + err.message);
                    }
                })();
            }

            // Create new invoice functions
            async function showNewInvoiceModal() {
                // Create modal if it doesn't exist
                let modal = document.getElementById('invoiceModal');
                if (!modal) {
                    modal = document.createElement('div');
                    modal.id = 'invoiceModal';
                    modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                    modal.innerHTML = 
                        '<div class="bg-white rounded-lg w-full max-w-4xl max-h-screen overflow-y-auto p-6">' +
                            '<div class="flex justify-between items-center mb-4">' +
                                '<h2 class="text-2xl font-bold text-gray-900">Factură nouă</h2>' +
                                '<button onclick="closeInvoiceModal()" class="text-gray-500 hover:text-gray-700">' +
                                    '<i class="fas fa-times"></i>' +
                                '</button>' +
                            '</div>' +
                            '<form id="invoiceForm" class="space-y-6">' +
                                '<div class="grid grid-cols-1 md:grid-cols-2 gap-6">' +
                                    '<div>' +
                                        '<label class="block text-sm font-medium text-gray-700 mb-1">Client</label>' +
                                        '<select id="client_id" class="w-full border border-gray-300 rounded-md px-3 py-2" required>' +
                                            '<option value="">Selectează client</option>' +
                                        '</select>' +
                                    '</div>' +
                                    '<div>' +
                                        '<label class="block text-sm font-medium text-gray-700 mb-1">Serie factură</label>' +
                                        '<select id="series_id" class="w-full border border-gray-300 rounded-md px-3 py-2" required>' +
                                            '<option value="">Selectează serie</option>' +
                                        '</select>' +
                                    '</div>' +
                                    '<div>' +
                                        '<label class="block text-sm font-medium text-gray-700 mb-1">Data emiterii</label>' +
                                        '<input type="date" id="issue_date" class="w-full border border-gray-300 rounded-md px-3 py-2" required>' +
                                    '</div>' +
                                    '<div>' +
                                        '<label class="block text-sm font-medium text-gray-700 mb-1">Data scadentă</label>' +
                                        '<input type="date" id="due_date" class="w-full border border-gray-300 rounded-md px-3 py-2">' +
                                    '</div>' +
                                    '<div>' +
                                        '<label class="block text-sm font-medium text-gray-700 mb-1">Monedă</label>' +
                                        '<select id="currency" class="w-full border border-gray-300 rounded-md px-3 py-2">' +
                                            '<option value="RON">RON</option>' +
                                            '<option value="EUR">EUR</option>' +
                                            '<option value="USD">USD</option>' +
                                        '</select>' +
                                    '</div>' +
                                '</div>' +
                                
                                '<div>' +
                                    '<div class="flex justify-between items-center mb-2">' +
                                        '<h3 class="text-lg font-medium text-gray-900">Produse și servicii</h3>' +
                                        '<button type="button" onclick="addInvoiceItem()" class="text-sm text-blue-600 hover:text-blue-800">' +
                                            '<i class="fas fa-plus mr-1"></i> Adaugă produs' +
                                        '</button>' +
                                    '</div>' +
                                    '<div class="overflow-x-auto">' +
                                        '<table class="min-w-full divide-y divide-gray-200">' +
                                            '<thead class="bg-gray-50">' +
                                                '<tr>' +
                                                    '<th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produs</th>' +
                                                    '<th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Denumire</th>' +
                                                    '<th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cantitate</th>' +
                                                    '<th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Preț unitar</th>' +
                                                    '<th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TVA %</th>' +
                                                    '<th scope="col" class="px-3 py-2"></th>' +
                                                '</tr>' +
                                            '</thead>' +
                                            '<tbody id="invoice_items" class="bg-white divide-y divide-gray-200">' +
                                                '<!-- Items will be added here -->' +
                                            '</tbody>' +
                                        '</table>' +
                                    '</div>' +
                                '</div>' +
                                
                                '<div>' +
                                    '<label class="block text-sm font-medium text-gray-700 mb-1">Note</label>' +
                                    '<textarea id="notes" class="w-full border border-gray-300 rounded-md px-3 py-2" rows="3"></textarea>' +
                                '</div>' +
                                
                                '<div class="flex justify-end space-x-3">' +
                                    '<button type="button" onclick="closeInvoiceModal()" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">' +
                                        'Anulează' +
                                    '</button>' +
                                    '<button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">' +
                                        'Salvează factura' +
                                    '</button>' +
                                '</div>' +
                            '</form>' +
                        '</div>';
                    document.body.appendChild(modal);
                    
                    // Set today's date as default
                    const today = new Date().toISOString().split('T')[0];
                    document.getElementById('issue_date').value = today;
                    
                    // Calculate due date (30 days from today)
                    const dueDate = new Date();
                    dueDate.setDate(dueDate.getDate() + 30);
                    document.getElementById('due_date').value = dueDate.toISOString().split('T')[0];
                    
                    // Add form submit handler
                    document.getElementById('invoiceForm').addEventListener('submit', saveInvoice);
                } else {
                    modal.style.display = 'flex';
                }
                
                // Load data
                fetchClients();
                fetchSeries();
                fetchProducts();
                
                // Add initial item
                if (document.getElementById('invoice_items').children.length === 0) {
                    addInvoiceItem();
                }
            }
            
            function closeInvoiceModal() {
                const modal = document.getElementById('invoiceModal');
                if (modal) {
                    modal.style.display = 'none';
                }
            }
            
            async function fetchClients() {
                try {
                    const token = localStorage.getItem('authToken');
                    const res = await fetch('/api/clients', {
                        headers: {
                            'Authorization': 'Bearer ' + token
                        }
                    });
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea clienților');
                    
                    const select = document.getElementById('client_id');
                    if (!select) return;
                    
                    // Keep the first option (placeholder)
                    const firstOption = select.options[0];
                    select.innerHTML = '';
                    select.appendChild(firstOption);
                    
                    data.data.forEach(client => {
                        const option = document.createElement('option');
                        option.value = client.id;
                        option.text = client.name + (client.cui ? ' (CUI: ' + client.cui + ')' : '');
                        select.appendChild(option);
                    });
                } catch (err) {
                    console.error(err);
                    alert(err.message || 'Eroare la încărcarea clienților');
                }
            }
            
            async function fetchSeries() {
                try {
                    const token = localStorage.getItem('authToken');
                    const res = await fetch('/api/settings/series', {
                        headers: {
                            'Authorization': 'Bearer ' + token
                        }
                    });
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea seriilor');
                    
                    const select = document.getElementById('series_id');
                    if (!select) return;
                    
                    // Keep the first option (placeholder)
                    const firstOption = select.options[0];
                    select.innerHTML = '';
                    select.appendChild(firstOption);
                    
                    // Filter only active invoice series
                    const invoiceSeries = data.data.filter(series => series.type === 'invoice' && series.active);
                    
                    invoiceSeries.forEach(series => {
                        const option = document.createElement('option');
                        option.value = series.id;
                        option.text = series.series + ' (' + series.year + ')';
                        select.appendChild(option);
                    });
                } catch (err) {
                    console.error(err);
                    // Fallback if /api/settings/series doesn't exist
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/invoices/series', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea seriilor');
                        
                        const select = document.getElementById('series_id');
                        if (!select) return;
                        
                        // Keep the first option (placeholder)
                        const firstOption = select.options[0];
                        select.innerHTML = '';
                        select.appendChild(firstOption);
                        
                        data.data.forEach(series => {
                            const option = document.createElement('option');
                            option.value = series.id;
                            option.text = series.series + ' (' + series.year + ')';
                            select.appendChild(option);
                        });
                    } catch (innerErr) {
                        console.error(innerErr);
                        // If both fail, just add a default option
                        const select = document.getElementById('series_id');
                        if (select && select.options.length <= 1) {
                            const option = document.createElement('option');
                            option.value = "1";
                            option.text = "FACT (2025)";
                            select.appendChild(option);
                        }
                    }
                }
            }
            
            let productsCache = [];
            
            async function fetchProducts() {
                try {
                    const token = localStorage.getItem('authToken');
                    const res = await fetch('/api/products', {
                        headers: {
                            'Authorization': 'Bearer ' + token
                        }
                    });
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea produselor');
                    
                    // Cache products for use in item rows
                    productsCache = data.data || [];
                } catch (err) {
                    console.error(err);
                    alert(err.message || 'Eroare la încărcarea produselor');
                }
            }
            
            function addInvoiceItem() {
                const itemsContainer = document.getElementById('invoice_items');
                const itemRow = document.createElement('tr');
                const itemIndex = itemsContainer.children.length;
                
                itemRow.innerHTML = 
                    '<td class="px-3 py-2">' +
                        '<select class="w-full border border-gray-300 rounded-md px-2 py-1 product-select" data-index="' + itemIndex + '" onchange="updateProductDetails(this)">' +
                            '<option value="">Selectează produs</option>' +
                            productsCache.map(product => '<option value="' + product.id + '">' + product.name + '</option>').join('') +
                        '</select>' +
                    '</td>' +
                    '<td class="px-3 py-2">' +
                        '<input type="text" name="items[' + itemIndex + '][name]" class="w-full border border-gray-300 rounded-md px-2 py-1" required>' +
                    '</td>' +
                    '<td class="px-3 py-2">' +
                        '<input type="number" name="items[' + itemIndex + '][quantity]" min="0.01" step="0.01" value="1" class="w-full border border-gray-300 rounded-md px-2 py-1" required>' +
                    '</td>' +
                    '<td class="px-3 py-2">' +
                        '<input type="number" name="items[' + itemIndex + '][unit_price]" min="0.01" step="0.01" value="0" class="w-full border border-gray-300 rounded-md px-2 py-1" required>' +
                    '</td>' +
                    '<td class="px-3 py-2">' +
                        '<select name="items[' + itemIndex + '][vat_rate]" class="w-full border border-gray-300 rounded-md px-2 py-1">' +
                            '<option value="19">19%</option>' +
                            '<option value="9">9%</option>' +
                            '<option value="5">5%</option>' +
                            '<option value="0">0%</option>' +
                        '</select>' +
                    '</td>' +
                    '<td class="px-3 py-2">' +
                        '<button type="button" class="text-red-500 hover:text-red-700" onclick="this.closest(&quot;tr&quot;).remove()">' +
                            '<i class="fas fa-trash"></i>' +
                        '</button>' +
                    '</td>';
                
                itemsContainer.appendChild(itemRow);
            }
            
            function updateProductDetails(selectElement) {
                const productId = selectElement.value;
                if (!productId) return;
                
                const product = productsCache.find(p => p.id == productId);
                if (!product) return;
                
                const row = selectElement.closest('tr');
                row.querySelector('input[name$="[name]"]').value = product.name;
                row.querySelector('input[name$="[unit_price]"]').value = product.price;
                
                // Find and select the correct VAT rate
                const vatSelect = row.querySelector('select[name$="[vat_rate]"]');
                for (let i = 0; i < vatSelect.options.length; i++) {
                    if (vatSelect.options[i].value == product.vat_rate) {
                        vatSelect.selectedIndex = i;
                        break;
                    }
                }
            }
            
            async function saveInvoice(e) {
                e.preventDefault();
                
                // Get form values
                const clientId = document.getElementById('client_id').value;
                const seriesId = document.getElementById('series_id').value;
                const issueDate = document.getElementById('issue_date').value;
                const dueDate = document.getElementById('due_date').value;
                const currency = document.getElementById('currency').value;
                const notes = document.getElementById('notes').value;
                
                // Get items
                const items = [];
                document.querySelectorAll('#invoice_items tr').forEach(row => {
                    const productSelect = row.querySelector('.product-select');
                    const nameInput = row.querySelector('input[name$="[name]"]');
                    const quantityInput = row.querySelector('input[name$="[quantity]"]');
                    const priceInput = row.querySelector('input[name$="[unit_price]"]');
                    const vatSelect = row.querySelector('select[name$="[vat_rate]"]');
                    
                    if (nameInput && nameInput.value.trim() !== '') {
                        items.push({
                            product_id: productSelect.value || null,
                            name: nameInput.value,
                            quantity: parseFloat(quantityInput.value),
                            unit_price: parseFloat(priceInput.value),
                            vat_rate: parseFloat(vatSelect.value)
                        });
                    }
                });
                
                if (items.length === 0) {
                    alert('Adaugă cel puțin un produs sau serviciu.');
                    return;
                }
                
                // Create invoice data
                const invoiceData = {
                    client_id: parseInt(clientId),
                    series_id: parseInt(seriesId),
                    issue_date: issueDate,
                    due_date: dueDate || null,
                    currency,
                    notes,
                    items
                };
                
                try {
                    const token = localStorage.getItem('authToken');
                    const res = await fetch('/api/invoices', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + token
                        },
                        body: JSON.stringify(invoiceData)
                    });
                    
                    const data = await res.json();
                    if (!res.ok) throw new Error(data.message || 'Eroare la crearea facturii');
                    
                    // Show success message
                    alert('Factura a fost creată cu succes: ' + data.data.number);
                    
                    // Close modal and refresh invoices
                    closeInvoiceModal();
                    fetchInvoices();
                    
                } catch (err) {
                    console.error(err);
                    alert(err.message || 'Eroare la crearea facturii');
                }
            }

            // Wrapper removed; use fetchClientsForTable() directly

            function fetchClientsForTable() {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/clients', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea clienților');

                        const tbody = document.getElementById('clientsTableBody');
                        if (!tbody) return;
                        tbody.innerHTML = '';

                        const clients = data.data || [];

                        if (clients.length === 0) {
                            const row = document.createElement('tr');
                            row.innerHTML = '<td colspan="7" class="px-6 py-4 text-center text-gray-500">Nu există clienți.</td>';
                            tbody.appendChild(row);
                            return;
                        }

                        for (const client of clients) {
                            const tr = document.createElement('tr');
                            const statusBadge = client.is_active ?
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Activ</span>' :
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactiv</span>';

                            let rowContent = '';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + client.name + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + (client.type === 'company' ? 'Companie' : 'Persoană fizică') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + (client.cui || client.cnp || '-') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + (client.email || '-') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + (client.phone || '-') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-center">' + statusBadge + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">';
                            rowContent += '<button onclick="editClient(' + client.id + ')" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Editează"><i class="fas fa-edit"></i></button>';
                            rowContent += '<button onclick="deleteClient(' + client.id + ')" class="text-red-600 hover:text-red-900" title="Șterge"><i class="fas fa-trash"></i></button>';
                            rowContent += '</td>';

                            tr.innerHTML = rowContent;
                            tbody.appendChild(tr);
                        }
                    } catch (err) {
                        console.error(err);
                        const tbody = document.getElementById('clientsTableBody');
                        if (tbody) {
                            tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center text-red-500">Eroare la încărcarea clienților: ' + err.message + '</td></tr>';
                        }
                    }
                })();
            }

            function fetchProductsForTable() {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/products', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la încărcarea produselor');

                        const tbody = document.getElementById('productsTableBody');
                        if (!tbody) return;
                        tbody.innerHTML = '';

                        const products = data.data || [];

                        if (products.length === 0) {
                            const row = document.createElement('tr');
                            row.innerHTML = '<td colspan="7" class="px-6 py-4 text-center text-gray-500">Nu există produse.</td>';
                            tbody.appendChild(row);
                            return;
                        }

                        for (const product of products) {
                            const tr = document.createElement('tr');
                            const statusBadge = product.active ?
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Activ</span>' :
                                '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactiv</span>';

                            let rowContent = '';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + product.name + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap">' + (product.is_service ? 'Serviciu' : 'Produs') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right">' + (product.price || 0).toFixed(2) + ' RON</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right">' + (product.vat_rate || 0) + '%</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right">' + (product.current_stock || '-') + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-center">' + statusBadge + '</td>';
                            rowContent += '<td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">';
                            rowContent += '<button onclick="editProduct(' + product.id + ')" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Editează"><i class="fas fa-edit"></i></button>';
                            rowContent += '<button onclick="deleteProduct(' + product.id + ')" class="text-red-600 hover:text-red-900" title="Șterge"><i class="fas fa-trash"></i></button>';
                            rowContent += '</td>';

                            tr.innerHTML = rowContent;
                            tbody.appendChild(tr);
                        }
                    } catch (err) {
                        console.error(err);
                        const tbody = document.getElementById('productsTableBody');
                        if (tbody) {
                            tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center text-red-500">Eroare la încărcarea produselor: ' + err.message + '</td></tr>';
                        }
                    }
                })();
            }

            // Modal and action functions
            function showNewClientModal() {
                alert('Funcționalitatea pentru adăugarea clienților va fi implementată în curând!');
            }

            function showNewProductModal() {
                alert('Funcționalitatea pentru adăugarea produselor va fi implementată în curând!');
            }

            function showNewProformaModal() {
                alert('Funcționalitatea pentru proforma va fi implementată în curând!');
            }

            function showNewReceiptModal() {
                alert('Funcționalitatea pentru chitanțe va fi implementată în curând!');
            }

            function editClient(id) {
                alert('Editarea clienților va fi implementată în curând!');
            }

            function deleteClient(id) {
                if (confirm('Sigur doriți să ștergeți acest client?')) {
                    alert('Funcționalitatea va fi implementată în curând!');
                }
            }

            function editProduct(id) {
                alert('Editarea produselor va fi implementată în curând!');
            }

            function deleteProduct(id) {
                if (confirm('Sigur doriți să ștergeți acest produs?')) {
                    alert('Funcționalitatea va fi implementată în curând!');
                }
            }

            // Report generation functions
            function generateSalesReport(period) {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/reports/sales?period=' + period, {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la generarea raportului');

                        displayReportResults('Raport vânzări - ' + period, data.data);
                    } catch (err) {
                        console.error(err);
                        alert('Eroare la generarea raportului: ' + err.message);
                    }
                })();
            }

            function generateVATReport(period) {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/reports/vat?period=' + period, {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la generarea raportului TVA');

                        displayReportResults('Raport TVA - ' + period, data.data);
                    } catch (err) {
                        console.error(err);
                        alert('Eroare la generarea raportului TVA: ' + err.message);
                    }
                })();
            }

            function generateClientReport() {
                (async function() {
                    try {
                        const token = localStorage.getItem('authToken');
                        const res = await fetch('/api/reports/clients', {
                            headers: {
                                'Authorization': 'Bearer ' + token
                            }
                        });
                        const data = await res.json();
                        if (!res.ok) throw new Error(data.message || 'Eroare la generarea raportului clienți');

                        displayReportResults('Analiza clienți', data.data);
                    } catch (err) {
                        console.error(err);
                        alert('Eroare la generarea raportului clienți: ' + err.message);
                    }
                })();
            }

            function generatePaymentReport() {
                alert('Raportul de comportament plăți va fi implementat în curând!');
            }

            function generateProductReport() {
                alert('Raportul de analiză produse va fi implementat în curând!');
            }

            function displayReportResults(title, data) {
                const reportResults = document.getElementById('reportResults');
                const reportContent = document.getElementById('reportContent');

                if (reportResults && reportContent) {
                    reportContent.innerHTML =
                        '<h4 class="font-semibold mb-4">' + title + '</h4>' +
                        '<div class="bg-gray-50 p-4 rounded-lg">' +
                            '<pre class="text-sm text-gray-700 whitespace-pre-wrap">' +
                            JSON.stringify(data, null, 2) +
                            '</pre>' +
                        '</div>';
                    reportResults.style.display = 'block';
                    reportResults.scrollIntoView({ behavior: 'smooth' });
                }
            }

            async function fetchCreditNotes() {
                const container = document.getElementById('credit-notes');
                container.innerHTML = '<h2 class="text-2xl font-bold mb-4">Note de Credit (Storno)</h2><p>Se încarcă...</p>';
                
                try {
                    const response = await fetchWithAuth('/api/credit-notes');
                    if (!response.ok) throw new Error('Failed to fetch credit notes');
                    const data = await response.json();
                    
                    let tableHtml = `
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-2xl font-bold">Note de Credit (Storno)</h2>
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg" onclick="showNewCreditNoteModal()">
                                <i class="fas fa-plus mr-2"></i>Notă de credit nouă
                            </button>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Număr</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Emiterii</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Factura Stornată</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">`;
                    
                    if (!data.data.credit_notes || data.data.credit_notes.length === 0) {
                        tableHtml += '<tr><td colspan="6" class="text-center py-4">Nu există note de credit.</td></tr>';
                    } else {
                        data.data.credit_notes.forEach(cn => {
                            tableHtml += `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">${cn.number}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">${new Date(cn.issue_date).toLocaleDateString('ro-RO')}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">${cn.client_name}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">${cn.original_invoice_number}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right">${cn.total.toFixed(2)} ${cn.currency}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right">
                                        <a href="/api/credit-notes/${cn.id}/pdf" target="_blank" class="text-indigo-600 hover:text-indigo-900 mr-3" title="Descarcă PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                        <a href="#" onclick="viewCreditNote(${cn.id})" class="text-blue-600 hover:text-blue-900" title="Vizualizare">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>`;
                        });
                    }
                    
                    tableHtml += `</tbody></table></div>`;
                    container.innerHTML = tableHtml;
                } catch (error) {
                    container.innerHTML = '<h2 class="text-2xl font-bold mb-4">Note de Credit (Storno)</h2><p class="text-red-500">Eroare la încărcarea notelor de credit.</p>';
                    console.error(error);
                }
            }

            async function fetchReceipts() {
                const container = document.getElementById('receipts');
                container.innerHTML = '<h2 class="text-2xl font-bold mb-4">Chitanțe</h2><p>Se încarcă...</p>';
                
                try {
                    const response = await fetchWithAuth('/api/receipts');
                    if (!response.ok) throw new Error('Failed to fetch receipts');
                    const data = await response.json();
                    
                    let tableHtml = `
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-2xl font-bold">Chitanțe</h2>
                            <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg" onclick="showNewReceiptModal()">
                                <i class="fas fa-plus mr-2"></i>Chitanță nouă
                            </button>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Număr</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data Emiterii</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Factura Aferentă</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Suma</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Metodă Plată</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">`;
                    
                    if (!data.data || data.data.length === 0) {
                        tableHtml += '<tr><td colspan="6" class="text-center py-4">Nu există chitanțe.</td></tr>';
                    } else {
                        data.data.forEach(r => {
                            tableHtml += `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">${r.number}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">${new Date(r.issue_date).toLocaleDateString('ro-RO')}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">${r.invoice_number}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right">${r.amount.toFixed(2)} ${r.currency}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right">${r.payment_method}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right">
                                        <a href="/api/receipts/${r.id}/pdf" target="_blank" class="text-indigo-600 hover:text-indigo-900" title="Descarcă PDF">
                                            <i class="fas fa-file-pdf"></i>
                                        </a>
                                    </td>
                                </tr>`;
                        });
                    }
                    
                    tableHtml += `</tbody></table></div>`;
                    container.innerHTML = tableHtml;
                } catch (error) {
                    container.innerHTML = '<h2 class="text-2xl font-bold mb-4">Chitanțe</h2><p class="text-red-500">Eroare la încărcarea chitanțelor.</p>';
                    console.error(error);
                }
            }

            // Placeholder functions for modal functionality
            function showNewCreditNoteModal() {
                // Will implement this later when we build the modal
                alert("Crearea notelor de credit va fi implementată în curând.");
            }

            function showNewReceiptModal() {
                // Will implement this later when we build the modal
                alert("Crearea chitanțelor va fi implementată în curând.");
            }

            function viewCreditNote(id) {
                // Will implement this later
                alert(`Vizualizare notă de credit cu ID-ul ${id} va fi implementată în curând.`);
            }
        </script>
    </body>
    </html>
  `);
});

// API Routes
app.route('/api/auth', authRoutes);

// Protected routes (require authentication)
app.use('/api/*', authMiddleware);
app.route('/api/invoices', invoices);
app.route('/api/clients', clientRoutes);
app.route('/api/anaf', anaf);
app.route('/api/products', productRoutes);
app.route('/api/companies', companyRoutes);
app.route('/api/dashboard', dashboardRoutes);
app.route('/api/reports', reportRoutes);
app.route('/api/settings', settingsRoutes);
app.route('/api/payments', paymentRoutes);
app.route('/api/credit-notes', creditNoteRoutes);
app.route('/api/receipts', receiptRoutes);

// Migration route for adding new features
app.post('/api/migrate', async (c) => {
  try {
    // Read migration SQL
    const migrationSQL = `
-- Recurring invoices table
CREATE TABLE IF NOT EXISTS recurring_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    template_name TEXT NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'yearly')),
    interval_count INTEGER NOT NULL DEFAULT 1,
    start_date DATE NOT NULL,
    end_date DATE,
    next_generation_date DATE NOT NULL,
    last_generated_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    auto_send_email BOOLEAN DEFAULT FALSE,
    notes TEXT,
    payment_terms TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Recurring invoice items table
CREATE TABLE IF NOT EXISTS recurring_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recurring_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    sort_order INTEGER DEFAULT 0
);

-- Payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    payment_provider TEXT NOT NULL,
    payment_id TEXT NOT NULL,
    amount DECIMAL(12,4) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'RON',
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    webhook_data TEXT,
    notes TEXT
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_company ON recurring_invoices(company_id);
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_next_date ON recurring_invoices(next_generation_date);
CREATE INDEX IF NOT EXISTS idx_recurring_items_recurring ON recurring_invoice_items(recurring_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_invoice ON payment_transactions(invoice_id);
    `;

    // Execute migration
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
    for (const statement of statements) {
      if (statement.trim()) {
        await c.env.DB.prepare(statement).run();
      }
    }

    return c.json({
      success: true,
      message: 'Migration completed successfully'
    });
  } catch (error) {
    console.error('Migration error:', error);
    return c.json({
      success: false,
      message: 'Migration failed: ' + error.message
    }, 500);
  }
});

// Error handler
app.onError(errorHandler);

export default app; 