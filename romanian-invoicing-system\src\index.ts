import { Hono } from 'hono';
import type { D1Database, KVNamespace, R2Bucket, Fetcher, SendEmail } from '@cloudflare/workers-types';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { serveStatic } from 'hono/cloudflare-workers';
import { authRoutes } from './routes/auth';
import { invoices } from './routes/invoices';
import { clientRoutes } from './routes/clients';
import { anaf } from './routes/anaf';
import { productRoutes } from './routes/products';
import { companyRoutes } from './routes/companies';
import { dashboardRoutes } from './routes/dashboard';
import { reportRoutes } from './routes/reports';
import { settingsRoutes } from './routes/settings';
import { paymentRoutes } from './routes/payments';
import { creditNoteRoutes } from './routes/credit_notes';
import { receiptRoutes } from './routes/receipts';
import { authMiddleware } from './middleware/auth';
import { errorHandler } from './middleware/error-handler';

export interface Env {
  DB: D1Database;
  KV: KVNamespace;
  BUCKET: R2Bucket;
  EMAIL: SendEmail;
  JWT_SECRET: string;
  ENVIRONMENT: string;
  COMPANY_NAME: string;
  DEFAULT_TIMEZONE: string;
  ENCRYPTION_KEY: string;
  BROWSER: Fetcher;
  INVOICING_KV: KVNamespace;
  INVOICING_FILES: R2Bucket;
}

const app = new Hono<{ Bindings: Env }>();

// Global middleware
app.use('*', logger());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// Serve static assets
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
app.use('/assets/*', serveStatic({ root: './' }));

// Serve the main application HTML
app.get('/', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Facturare Românesc - API</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 font-sans">
    <div class="min-h-screen flex items-center justify-center">
        <div class="max-w-md w-full space-y-8 text-center">
            <div>
                <i class="fas fa-file-invoice text-6xl text-indigo-600 mb-4"></i>
                <h1 class="text-3xl font-bold text-gray-900">Sistem Facturare Românesc</h1>
                <p class="mt-2 text-lg text-gray-600">API pentru facturare conformă cu legislația română</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-xl font-semibold mb-4">API Endpoints</h2>
                <div class="space-y-2 text-left">
                    <p><strong>POST</strong> /api/auth/login - Autentificare</p>
                    <p><strong>POST</strong> /api/auth/register - Înregistrare</p>
                    <p><strong>GET</strong> /api/invoices - Lista facturi</p>
                    <p><strong>GET</strong> /api/clients - Lista clienți</p>
                    <p><strong>GET</strong> /api/dashboard/stats - Statistici</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`);

// Serve static assets
app.use('/assets/*', serveStatic({ root: './' }));












// API Routes
app.route('/api/auth', authRoutes);

// Protected routes (require authentication)
app.use('/api/*', authMiddleware);
app.route('/api/invoices', invoices);
app.route('/api/clients', clientRoutes);
app.route('/api/anaf', anaf);
app.route('/api/products', productRoutes);
app.route('/api/companies', companyRoutes);
app.route('/api/dashboard', dashboardRoutes);
app.route('/api/reports', reportRoutes);
app.route('/api/settings', settingsRoutes);
app.route('/api/payments', paymentRoutes);
app.route('/api/credit-notes', creditNoteRoutes);
app.route('/api/receipts', receiptRoutes);

// Migration route for adding new features
app.post('/api/migrate', async (c) => {
  try {
    // Read migration SQL
    const migrationSQL = `
-- Recurring invoices table
CREATE TABLE IF NOT EXISTS recurring_invoices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL,
    client_id INTEGER NOT NULL,
    series_id INTEGER NOT NULL,
    template_name TEXT NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'yearly')),
    interval_count INTEGER NOT NULL DEFAULT 1,
    start_date DATE NOT NULL,
    end_date DATE,
    next_generation_date DATE NOT NULL,
    last_generated_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    auto_send_email BOOLEAN DEFAULT FALSE,
    notes TEXT,
    payment_terms TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Recurring invoice items table
CREATE TABLE IF NOT EXISTS recurring_invoice_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    recurring_id INTEGER NOT NULL,
    product_id INTEGER,
    name TEXT NOT NULL,
    description TEXT,
    quantity DECIMAL(10,4) NOT NULL,
    unit TEXT NOT NULL DEFAULT 'buc',
    unit_price DECIMAL(10,4) NOT NULL,
    vat_rate DECIMAL(5,2) NOT NULL,
    sort_order INTEGER DEFAULT 0
);

-- Payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_id INTEGER NOT NULL,
    payment_provider TEXT NOT NULL,
    payment_id TEXT NOT NULL,
    amount DECIMAL(12,4) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'RON',
    status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    webhook_data TEXT,
    notes TEXT
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_company ON recurring_invoices(company_id);
CREATE INDEX IF NOT EXISTS idx_recurring_invoices_next_date ON recurring_invoices(next_generation_date);
CREATE INDEX IF NOT EXISTS idx_recurring_items_recurring ON recurring_invoice_items(recurring_id);
CREATE INDEX IF NOT EXISTS idx_payment_transactions_invoice ON payment_transactions(invoice_id);
    `;

    // Execute migration
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
    for (const statement of statements) {
      if (statement.trim()) {
        await c.env.DB.prepare(statement).run();
      }
    }

    return c.json({
      success: true,
      message: 'Migration completed successfully'
    });
  } catch (error) {
    console.error('Migration error:', error);
    return c.json({
      success: false,
      message: 'Migration failed: ' + error.message
    }, 500);
  }
});

// Error handler
app.onError(errorHandler);

export default app; 